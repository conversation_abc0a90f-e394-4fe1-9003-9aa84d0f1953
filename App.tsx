
import React, { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { Toolbar } from './components/Toolbar';
import { Canvas } from './components/Canvas';
import { LayersPanel } from './components/LayersPanel';
import { RotationControl } from './components/RotationControl';
import {
  type TokenState,
  type Annotation,
  type Tool,
  type Point,
  UnitType,
  AnnotationType,
  type DrawingState,
  type TextPreset,
  type OtherType,
  type CustomUnit,
  type ProjectData,
  type ViewTransform,
  type LayerEntry,
  type LayerItemIdentifier,
  type LayerGroup,
  type ArrowSettings,
  type ArrowAnnotation,
  type BrushSettings,
  type BrushStrokeAnnotation,
  type BrushPathSegment,
  type HistoryState,
  type ThemeSettings,
} from './types';
import { INITIAL_PALETTE_COLORS, FONT_FACES } from './constants';
import { translations } from './translations';

const getPathLength = (path: Point[]): number => {
    if (path.length < 2) return 0;
    return path.slice(1).reduce((acc, point, i) => acc + Math.hypot(point.x - path[i].x, point.y - path[i].y), 0);
};

const getPointOnPath = (path: Point[], progress: number): Point => {
    if (path.length < 2) return path[0] || { x: 0, y: 0 };
    const totalLength = getPathLength(path);
    if(totalLength === 0) return path[0];

    let distanceTraveled = progress * totalLength;
    
    for (let i = 0; i < path.length - 1; i++) {
        const p1 = path[i];
        const p2 = path[i+1];
        const segmentLength = Math.hypot(p2.x - p1.x, p2.y - p1.y);
        
        if (distanceTraveled <= segmentLength) {
            const segmentProgress = segmentLength === 0 ? 0 : distanceTraveled / segmentLength;
            return {
                x: p1.x + (p2.x - p1.x) * segmentProgress,
                y: p1.y + (p2.y - p1.y) * segmentProgress
            };
        }
        distanceTraveled -= segmentLength;
    }
    return path[path.length - 1];
};

const distSq = (p1: Point, p2: Point) => (p1.x - p2.x) ** 2 + (p1.y - p2.y) ** 2;

const distToSegmentSq = (p: Point, v: Point, w: Point) => {
    const l2 = distSq(v, w);
    if (l2 === 0) return distSq(p, v);
    let t = ((p.x - v.x) * (w.x - v.x) + (p.y - v.y) * (w.y - v.y)) / l2;
    t = Math.max(0, Math.min(1, t));
    const projection = { x: v.x + t * (w.x - v.x), y: v.y + t * (w.y - v.y) };
    return distSq(p, projection);
};

interface NewTextState {
    content: string;
    size: number; // in rem
    fontFamily: string;
    outlineColor1: string;
    outlineColor2: string;
    outlineWidth: number;
    isNeon: boolean;
    glowColor: string;
    glowSize: number;
}

const toolShortcuts: { [key: string]: Tool } = {
  s: 'select',
  m: 'move',
  w: 'brush',
  d: 'clone',
  z: 'zoom',
  e: 'enlarge',
  t: 'text',
  b: 'eraser',
  f: 'arrow',
  c: 'circle',
};

// Helper to find a group recursively
const findGroupRec = (entries: LayerEntry[], id: number): LayerGroup | null => {
    for (const entry of entries) {
        if (entry.type === 'group') {
            if (entry.id === id) return entry;
            const found = findGroupRec(entry.items, id);
            if (found) return found;
        }
    }
    return null;
};

// Helper to get all items (tokens/annotations) from a group, including nested groups
const getAllItemIdsFromGroup = (group: LayerGroup): { tokens: Set<number>, annotations: Set<number> } => {
    const ids: { tokens: Set<number>, annotations: Set<number> } = { tokens: new Set(), annotations: new Set() };
    const collect = (items: LayerEntry[]) => {
        for (const item of items) {
            if (item.type === 'group') {
                collect(item.items);
            } else if (item.type === 'token') {
                ids.tokens.add(item.id);
            } else {
                ids.annotations.add(item.id);
            }
        }
    };
    collect(group.items);
    return ids;
};

const INITIAL_THEME_SETTINGS: ThemeSettings = {
    gradientColors: ['#2d3748', '#1a202c', '#1a202c'],
    gradientAngle: 145,
    textColor: '#FFFFFF',
    uiScale: 1.0,
    buttonColor: '#2563eb', // Default blue
};


const App: React.FC = () => {
  const [mapImage, setMapImage] = useState<string | null>(null);
  const [mapVideo, setMapVideo] = useState<string | null>(null);
  const [mapDimensions, setMapDimensions] = useState<{ width: number; height: number; } | null>(null);
  const [tokens, setTokens] = useState<TokenState[]>([]);
  const [annotations, setAnnotations] = useState<Annotation[]>([]);
  const [layers, setLayers] = useState<LayerEntry[]>([]);
  const [history, setHistory] = useState<HistoryState[]>([]);
  const [redoStack, setRedoStack] = useState<HistoryState[]>([]);
  const [viewTransform, setViewTransform] = useState<ViewTransform | null>(null);
  const [language, setLanguage] = useState<'en' | 'es'>('es');
  const [mapRotation, setMapRotation] = useState<number>(0);
  const [isMapRotating, setIsMapRotating] = useState<boolean>(false);

  const [isVideoPlaying, setIsVideoPlaying] = useState(true);
  const [videoCurrentTime, setVideoCurrentTime] = useState(0);
  const [videoDuration, setVideoDuration] = useState(0);

  const t = useCallback((key: keyof typeof translations): string => {
    const translation = translations[key];
    if (translation) {
      return translation[language] || key;
    }
    return key;
  }, [language]);

  const [activeTool, setActiveTool] = useState<Tool>('select');
  const [selectedItemIds, setSelectedItemIds] = useState<LayerItemIdentifier[]>([]);
  const [drawingState, setDrawingState] = useState<DrawingState | null>(null);
  const [paletteColors, setPaletteColors] = useState<string[]>(INITIAL_PALETTE_COLORS);
  const [selectedColor1, setSelectedColor1] = useState<string>(INITIAL_PALETTE_COLORS[0]);
  const [selectedColor2, setSelectedColor2] = useState<string>(INITIAL_PALETTE_COLORS[1]);
  const [editingTokenId, setEditingTokenId] = useState<number | null>(null);
  const [newText, setNewText] = useState<NewTextState>(() => ({
    content: t('text_creator_default_text'),
    size: 1.5, // rem
    fontFamily: FONT_FACES[0],
    outlineColor1: '#FFFFFF',
    outlineColor2: '#000080',
    outlineWidth: 0.15,
    isNeon: false,
    glowColor: '#00BFFF',
    glowSize: 5.0,
  }));
  const [lastOutlineWidth, setLastOutlineWidth] = useState<number>(0.15);
  const [textPresets, setTextPresets] = useState<TextPreset[]>(() => {
    try {
      const saved = localStorage.getItem('textPresets');
      return saved ? JSON.parse(saved) : [];
    } catch (error) {
      console.error('Failed to load text presets from localStorage', error);
      return [];
    }
  });
  const [arrowSettings, setArrowSettings] = useState<ArrowSettings>({
    color: '#FF0000',
    strokeWidthStart: 17.0,
    strokeWidthEnd: 3.0,
    arrowheadLength: 18,
    arrowheadWidth: 19,
    isAnimated: true,
    animatingCircleRadius: 10,
    animatingCircleColor: '#FFFF00',
    animationDuration: 2.0,
    isNeon: false,
    glowColor: '#00BFFF',
    glowSize: 5.0,
  });
  const [brushSettings, setBrushSettings] = useState<BrushSettings>({
    color: '#FFFFFF',
    size: 12,
    opacity: 1,
    effect: 'none',
    glowColor: '#FFFF00',
    glowSize: 5.0,
    isNeon: false,
    smoothing: 5,
  });
  const [themeSettings, setThemeSettings] = useState<ThemeSettings>(() => {
    try {
        const savedSettings = localStorage.getItem('themeSettings');
        if (savedSettings) {
            const parsed = JSON.parse(savedSettings);
            // Merge with defaults to ensure all keys are present
            return { ...INITIAL_THEME_SETTINGS, ...parsed };
        }
    } catch (error) {
        console.error("Failed to load theme settings from localStorage", error);
    }
    return INITIAL_THEME_SETTINGS;
  });
  const [customUnits, setCustomUnits] = useState<CustomUnit[]>([]);
  const [customOthers, setCustomOthers] = useState<CustomUnit[]>([]);
  const [globalTokenSize, setGlobalTokenSize] = useState<number>(0);
  const [arePathsVisible, setArePathsVisible] = useState<boolean>(true);
  const [currentBrushAnnotation, setCurrentBrushAnnotation] = useState<BrushStrokeAnnotation | null>(null);
  const [brushRedoStack, setBrushRedoStack] = useState<BrushPathSegment[]>([]);

  const nextId = useRef(1);
  const animationFrameId = useRef<number | null>(null);
  const lastClick = useRef<{ time: number; tokenId: number | null }>({ time: 0, tokenId: null });
  const canvasRef = useRef<HTMLDivElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  const selectedToken = useMemo(() => {
    if (selectedItemIds.length === 1 && selectedItemIds[0].type === 'token') {
      return tokens.find(t => t.id === selectedItemIds[0].id) ?? null;
    }
    return null;
  }, [selectedItemIds, tokens]);

  const getNextId = useCallback(() => {
    nextId.current += 1;
    return nextId.current;
  }, []);

  const saveStateToHistory = useCallback(() => {
    const tokensForHistory = tokens.map(token => {
      // If a token is on a path, its canonical state for history purposes is at the start of the path.
      // This prevents saving transient animation positions.
      if (token.path && token.path.length > 0) {
        return {
          ...token,
          position: token.path[0],
          animationProgress: 0,
          patrolForward: true,
        };
      }
      return token;
    });

    setHistory(prev => [...prev, { tokens: tokensForHistory, annotations, layers, customUnits, customOthers }]);
    setRedoStack([]);
  }, [tokens, annotations, layers, customUnits, customOthers]);

  useEffect(() => {
    if (activeTool !== 'brush' && currentBrushAnnotation) {
        // Commit the brush annotation when switching away from the brush tool
        saveStateToHistory();
        
        const finalAnnotation = { ...currentBrushAnnotation, id: getNextId() };
        
        setAnnotations(prev => [...prev, finalAnnotation]);
        setLayers(prev => [{id: finalAnnotation.id, type: 'annotation'}, ...prev]);
        
        setCurrentBrushAnnotation(null);
        setBrushRedoStack([]);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTool, currentBrushAnnotation]);

  useEffect(() => {
    // Update default text when language changes, only if it's still the default
    if (newText.content === translations.text_creator_default_text.en || newText.content === translations.text_creator_default_text.es) {
      setNewText(nt => ({...nt, content: t('text_creator_default_text')}));
    }
  }, [t, newText.content]);

  useEffect(() => {
    try {
      localStorage.setItem('textPresets', JSON.stringify(textPresets));
    } catch (error) {
      console.error('Failed to save text presets to localStorage', error);
    }
  }, [textPresets]);
  
  useEffect(() => {
    try {
        localStorage.setItem('themeSettings', JSON.stringify(themeSettings));
    } catch (error) {
        console.error("Failed to save theme settings from localStorage", error);
    }
    document.documentElement.style.setProperty('font-size', `${16 * themeSettings.uiScale}px`);
    return () => {
        document.documentElement.style.removeProperty('font-size');
    };
  }, [themeSettings]);

  useEffect(() => {
    if (selectedToken) {
      const needsUpdate = selectedToken.color !== selectedColor1 || 
                          selectedToken.outlineColor1 !== selectedColor1 ||
                          selectedToken.outlineColor2 !== selectedColor2;

      if (needsUpdate) {
        handleTokenUpdate(selectedToken.id, { 
            color: selectedColor1, 
            outlineColor1: selectedColor1, 
            outlineColor2: selectedColor2 
        });
      }
    }
    // This effect should not depend on handleTokenUpdate, as it would cause re-renders.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedColor1, selectedColor2, selectedToken]);

  const handleUndo = useCallback(() => {
    // Local undo for brush tool
    if (activeTool === 'brush') {
      if (currentBrushAnnotation && currentBrushAnnotation.segments.length > 0) {
        const newSegments = [...currentBrushAnnotation.segments];
        const lastSegment = newSegments.pop();
        
        if (lastSegment) {
          setBrushRedoStack(prev => [lastSegment, ...prev]);
        }

        if (newSegments.length > 0) {
            setCurrentBrushAnnotation({ ...currentBrushAnnotation, segments: newSegments });
        } else {
            setCurrentBrushAnnotation(null);
        }
      }
      return; // Stop here, don't touch global history for brush tool
    }
    
    // Global undo
    if (history.length === 0) return;

    const lastState = history[history.length - 1];
    const newHistory = history.slice(0, history.length - 1);

    setRedoStack(prev => [...prev, { tokens, annotations, layers, customUnits, customOthers }]);
    
    setTokens(lastState.tokens);
    setAnnotations(lastState.annotations);
    setLayers(lastState.layers);
    setCustomUnits(lastState.customUnits);
    setCustomOthers(lastState.customOthers);
    setHistory(newHistory);

    setSelectedItemIds([]);
    setDrawingState(null);
    setEditingTokenId(null);
    setActiveTool('select');
  }, [history, tokens, annotations, layers, customUnits, customOthers, activeTool, currentBrushAnnotation]);

  const handleRedo = useCallback(() => {
    // Local redo for brush tool
    if (activeTool === 'brush') {
      if (brushRedoStack.length > 0) {
        const newBrushRedoStack = [...brushRedoStack];
        const segmentToRedo = newBrushRedoStack.shift();

        if (segmentToRedo) {
          setCurrentBrushAnnotation(prev => {
            if (!prev) {
                return {
                    id: -1,
                    type: AnnotationType.BrushStroke,
                    segments: [segmentToRedo],
                    isVisible: true,
                };
            }
            return { ...prev, segments: [...prev.segments, segmentToRedo] };
          });
          setBrushRedoStack(newBrushRedoStack);
        }
      }
      return; // Stop here for brush tool
    }
    
    // Global redo
    if (redoStack.length === 0) return;

    const nextState = redoStack[redoStack.length - 1];
    const newRedoStack = redoStack.slice(0, redoStack.length - 1);

    setHistory(prev => [...prev, { tokens, annotations, layers, customUnits, customOthers }]);

    setTokens(nextState.tokens);
    setAnnotations(nextState.annotations);
    setLayers(nextState.layers);
    setCustomUnits(nextState.customUnits);
    setCustomOthers(nextState.customOthers);
    setRedoStack(newRedoStack);

    setSelectedItemIds([]);
    setDrawingState(null);
    setEditingTokenId(null);
    setActiveTool('select');
  }, [redoStack, tokens, annotations, layers, customUnits, customOthers, activeTool, brushRedoStack]);

  const flattenedItemsForCanvas = useMemo(() => {
    const finalItemMap = new Map<string, TokenState | Annotation>();
    tokens.forEach(t => finalItemMap.set(`token-${t.id}`, t));
    annotations.forEach(a => finalItemMap.set(`annotation-${a.id}`, a));

    const flatList: LayerItemIdentifier[] = [];
    const flatten = (items: LayerEntry[]) => {
      for (const item of items) {
        if (item.type === 'group') {
          // Always recurse into groups to get all items for canvas rendering.
          // The isCollapsed property only affects the LayersPanel UI.
          flatten(item.items);
        } else {
          flatList.push(item);
        }
      }
    };
    flatten(layers);

    return flatList
      .map(identifier => finalItemMap.get(`${identifier.type}-${identifier.id}`))
      .filter((item): item is TokenState | Annotation => !!item);
  }, [layers, tokens, annotations]);
  
  const findItemAtPoint = useCallback((point: Point): LayerItemIdentifier | null => {
      const TOKEN_HIT_RADIUS_SQ = 20 ** 2;
      const ANNOTATION_HIT_RADIUS_SQ = 15 ** 2;

      // Iterate from top to bottom
      for (const item of [...flattenedItemsForCanvas].reverse()) {
        if (item.isVisible === false) continue;
          
        if ('position' in item) { // TokenState
            const token = item as TokenState;
            const radius = TOKEN_HIT_RADIUS_SQ * ((token.size ?? 1) * (1 + globalTokenSize / 100)) ** 2;
            if (distSq(token.position, point) < radius) {
                return { id: token.id, type: 'token' };
            }
        } else { // Annotation
            const ann = item as Annotation;
            switch(ann.type) {
                case AnnotationType.Circle:
                    if (distSq(point, ann.center) <= ann.radius * ann.radius) {
                        return { id: ann.id, type: 'annotation' };
                    }
                    break;
                case AnnotationType.Arrow:
                    if (distToSegmentSq(point, ann.start, ann.control) < ANNOTATION_HIT_RADIUS_SQ ||
                        distToSegmentSq(point, ann.control, ann.end) < ANNOTATION_HIT_RADIUS_SQ) {
                        return { id: ann.id, type: 'annotation' };
                    }
                    break;
                case AnnotationType.BrushStroke:
                    for (const segment of ann.segments) {
                        const path = segment.path;
                        for (let i = 0; i < path.length - 1; i++) {
                            if (distToSegmentSq(point, path[i], path[i+1]) < (segment.settings.size / 2) ** 2) {
                                return { id: ann.id, type: 'annotation' };
                            }
                        }
                    }
                    break;
            }
        }
      }
      return null;
  }, [flattenedItemsForCanvas, globalTokenSize]);

  useEffect(() => {
    // Do not clear drawing state for brush tool to allow continuous drawing
    if (activeTool !== 'brush') {
        setDrawingState(null);
    }
  }, [activeTool]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      const target = e.target as HTMLElement;
      const isEditing = target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable;

      if (e.key === 'Escape') {
        setDrawingState(null);
        setEditingTokenId(null);
        setViewTransform(null);
        return;
      }

      if (isEditing) return;

      if (mapVideo && (e.key === ' ' || e.code === 'Space')) {
        e.preventDefault();
        setIsVideoPlaying(p => !p);
        return;
      }

      const key = e.key.toLowerCase();
      const isUndo = (e.ctrlKey || e.metaKey) && key === 'z' && !e.shiftKey;
      const isRedo = (e.ctrlKey || e.metaKey) && (key === 'y' || (key === 'z' && e.shiftKey));

      if (isUndo) {
        e.preventDefault();
        handleUndo();
      } else if (isRedo) {
        e.preventDefault();
        handleRedo();
      } else if (toolShortcuts[key]) {
        e.preventDefault();
        setActiveTool(toolShortcuts[key]);
      }
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleUndo, handleRedo, mapVideo]);

  const handleMapUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
        const file = event.target.files[0];

        const resetStateForNewMap = () => {
            setTokens([]);
            setAnnotations([]);
            setLayers([]);
            setSelectedItemIds([]);
            setDrawingState(null);
            setCurrentBrushAnnotation(null);
        };

        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const imageUrl = e.target?.result as string;
                if (!imageUrl) return;

                const img = new Image();
                img.onload = () => {
                    saveStateToHistory();
                    setMapImage(imageUrl);
                    setMapVideo(null);
                    setMapDimensions({ width: img.naturalWidth, height: img.naturalHeight });
                    resetStateForNewMap();
                };
                img.src = imageUrl;
            };
            reader.readAsDataURL(file);
        } else if (file.type === 'video/mp4') {
            const videoElement = document.createElement('video');
            videoElement.preload = 'metadata';
            videoElement.onloadedmetadata = () => {
                const reader = new FileReader();
                reader.onload = (readEvent) => {
                    const videoUrl = readEvent.target?.result as string;
                    if (!videoUrl) return;

                    saveStateToHistory();
                    setMapVideo(videoUrl);
                    setMapImage(null);
                    setMapDimensions({ width: videoElement.videoWidth, height: videoElement.videoHeight });
                    resetStateForNewMap();

                    setVideoCurrentTime(0);
                    setVideoDuration(videoElement.duration);
                    setIsVideoPlaying(true);
                };
                reader.readAsDataURL(file);
                URL.revokeObjectURL(videoElement.src);
            };
            videoElement.onerror = () => {
                console.error("Error loading video metadata.");
                URL.revokeObjectURL(videoElement.src);
            };
            videoElement.src = URL.createObjectURL(file);
        }
    }
  };

  const addToken = (type: UnitType, position: Point, options: Partial<TokenState> = {}) => {
    saveStateToHistory();
    const newId = getNextId();
    const newToken: TokenState = {
      id: newId,
      type,
      color: options.color || selectedColor1,
      number: null,
      position,
      path: [],
      animationProgress: 0,
      patrolForward: true,
      size: options.size ?? 1,
      rotation: options.rotation ?? 0,
      text: type === UnitType.Text ? options.text ?? 'Text' : undefined,
      fontFamily: type === UnitType.Text ? options.fontFamily : undefined,
      outlineColor1: options.outlineColor1 ?? selectedColor1,
      outlineColor2: options.outlineColor2 ?? selectedColor2,
      outlineWidth: options.outlineWidth ?? lastOutlineWidth,
      isNeon: type === UnitType.Text ? options.isNeon : undefined,
      glowColor: type === UnitType.Text ? options.glowColor : undefined,
      glowSize: type === UnitType.Text ? options.glowSize : undefined,
      otherType: type === UnitType.Other ? options.otherType : undefined,
      customImage: type === UnitType.Custom || type === UnitType.CustomOther ? options.customImage : undefined,
      isVisible: true,
      animationSpeed: options.animationSpeed ?? 0.20,
      isPatrol: options.isPatrol ?? false,
      isFlipped: options.isFlipped ?? false,
      isAnimating: true,
      name: options.name,
    };
    setTokens((prev) => [...prev, newToken]);
    setLayers(prev => [{id: newId, type: 'token'}, ...prev]);
    return newToken;
  }

  const handleTokenDrop = (data: string, position: Point) => {
    if (!mapDimensions) return;
    let parsedData;
    try {
        parsedData = JSON.parse(data);
    } catch (e) {
        console.error("Failed to parse dropped data:", e);
        return;
    }

    if (parsedData.isTextToken) {
        addToken(UnitType.Text, position, {
            text: parsedData.content,
            size: parsedData.size,
            fontFamily: parsedData.fontFamily,
            color: selectedColor1,
            outlineColor1: parsedData.outlineColor1,
            outlineColor2: parsedData.outlineColor2,
            outlineWidth: parsedData.outlineWidth,
            isNeon: parsedData.isNeon,
            glowColor: parsedData.glowColor,
            glowSize: parsedData.glowSize,
        });
    } else if (Object.values(UnitType).includes(parsedData.type)) {
        // All other unit types can use this generic logic
        const { type, ...options } = parsedData;
        addToken(type, position, { 
            ...options, // This includes customImage, name, otherType, rotation, outlineWidth etc.
            color: selectedColor1,
            outlineColor1: selectedColor1,
            outlineColor2: selectedColor2
        });
    }
  };

  const deleteItem = useCallback((id: number, type: 'token' | 'annotation') => {
    saveStateToHistory();
    if (type === 'token') {
        setTokens(prev => prev.filter(t => t.id !== id));
        if (editingTokenId === id) setEditingTokenId(null);
    } else {
        setAnnotations(prev => prev.filter(a => a.id !== id));
    }

    setSelectedItemIds(prev => prev.filter(item => !(item.id === id && item.type === type)));

    const removeItemRec = (entries: LayerEntry[]): LayerEntry[] => {
      return entries.reduce((acc, entry) => {
          if (entry.type === 'group') {
              const newItems = removeItemRec(entry.items || []); // Guard against undefined items
              if (newItems.length > 0) {
                   acc.push({ ...entry, items: newItems });
              }
          } else if (!(entry.id === id && entry.type === type)) {
              acc.push(entry);
          }
          return acc;
      }, [] as LayerEntry[]);
    };

    setLayers(prev => removeItemRec(prev));

  }, [editingTokenId, saveStateToHistory]);

  const handleZoom = useCallback((s1: Point, s2: Point, canvasRect: DOMRect | undefined) => {
    if (!canvasRect) return;

    const zoomRectWidth = Math.abs(s1.x - s2.x);
    const zoomRectHeight = Math.abs(s1.y - s2.y);
    
    // This is the scale factor needed to map the selection to the canvas
    const scaleX = canvasRect.width / zoomRectWidth;
    const scaleY = canvasRect.height / zoomRectHeight;
    const scale_map = Math.min(scaleX, scaleY, 8 / (viewTransform?.scale ?? 1)); // Cap final zoom at 8x
    
    // This is the translation needed to map the selection to the canvas
    const zoomCenterX_screen = Math.min(s1.x, s2.x) + zoomRectWidth / 2;
    const zoomCenterY_screen = Math.min(s1.y, s2.y) + zoomRectHeight / 2;
    const translate_map_x = (canvasRect.width / 2) - (zoomCenterX_screen * scale_map);
    const translate_map_y = (canvasRect.height / 2) - (zoomCenterY_screen * scale_map);

    // Compose with the existing viewTransform
    const old_scale = viewTransform?.scale ?? 1;
    const old_translateX = viewTransform?.translateX ?? 0;
    const old_translateY = viewTransform?.translateY ?? 0;

    const new_scale = scale_map * old_scale;
    const new_translateX = scale_map * old_translateX + translate_map_x;
    const new_translateY = scale_map * old_translateY + translate_map_y;

    setViewTransform({ scale: new_scale, translateX: new_translateX, translateY: new_translateY });

  }, [viewTransform]);

  const handleZoomReset = useCallback(() => {
    setViewTransform(null);
  }, []);

  const handleMouseDown = (point: Point, screenPoint: Point) => {
    if (!mapDimensions) return;
    
    const TOKEN_HIT_RADIUS = 20;

    if (activeTool === 'zoom') {
        setDrawingState({ type: 'zoom', start: screenPoint, current: screenPoint });
        return;
    }
    
    if (activeTool === 'enlarge') {
        const tokenToEnlarge = [...tokens].reverse().find(t => t.isVisible !== false && distSq(t.position, point) < (TOKEN_HIT_RADIUS * (t.size ?? 1)) ** 2);
        if (tokenToEnlarge) {
            saveStateToHistory();
            setDrawingState({
                type: 'enlarging',
                tokenId: tokenToEnlarge.id,
                startY: point.y,
                startSize: tokenToEnlarge.size ?? 1,
                current: point
            });
        }
        return;
    }

    if (activeTool === 'move') {
      const itemToMove = findItemAtPoint(point);
      if (itemToMove) {
          let canMove = false;
          if (itemToMove.type === 'token') {
              canMove = true;
          } else if (itemToMove.type === 'annotation') {
              const annotation = annotations.find(a => a.id === itemToMove.id);
              if (annotation && annotation.type === AnnotationType.BrushStroke) {
                  canMove = true;
              }
          }

          if (canMove) {
              saveStateToHistory();
              let offset: Point = { x: 0, y: 0 };
              if (itemToMove.type === 'token') {
                  const token = tokens.find(t => t.id === itemToMove.id);
                  if (token) offset = { x: point.x - token.position.x, y: point.y - token.position.y };
              } else {
                  // For brush strokes, offset is from the click point itself
                  offset = { x: 0, y: 0 };
              }
              setDrawingState({ type: 'moving', itemType: itemToMove.type, itemId: itemToMove.id, offset, current: point });
          }
      }
      return;
    }

    if (activeTool === 'clone') {
        const tokenToClone = [...tokens].reverse().find(t => t.isVisible !== false && distSq(t.position, point) < (TOKEN_HIT_RADIUS * (t.size ?? 1)) ** 2);
        if (tokenToClone) {
            setDrawingState({ type: 'cloning', tokenToClone: tokenToClone, current: point });
        }
        return;
    }

    if (activeTool === 'eraser') {
      const itemToDelete = findItemAtPoint(point);
      if (itemToDelete) {
        deleteItem(itemToDelete.id, itemToDelete.type);
        return;
      }
       const tokenWithPath = tokens.find(token => {
        if (!token.path || token.path.length < 2 || token.isVisible === false) return false;
        for (let i = 0; i < token.path.length - 1; i++) {
          if (distToSegmentSq(point, token.path[i], token.path[i+1]) < (TOKEN_HIT_RADIUS/2)**2) return true;
        }
        return false;
      });

      if (tokenWithPath) clearPath(tokenWithPath.id);
      return;
    }

    if (activeTool === 'select') {
        const clickedToken = tokens.find(t => t.isVisible !== false && distSq(t.position, point) < (TOKEN_HIT_RADIUS * (t.size ?? 1)) ** 2);
        
        if (clickedToken) {
            const now = Date.now();
            const isDoubleClick = clickedToken.type === UnitType.Text && 
                                  now - lastClick.current.time < 300 && 
                                  lastClick.current.tokenId === clickedToken.id;
    
            if (isDoubleClick) {
                // Double-click to edit text
                lastClick.current = { time: 0, tokenId: null }; // Reset click tracker
                setEditingTokenId(clickedToken.id);
                setSelectedItemIds([]); // Deselect to avoid confusion with path drawing
                setActiveTool('select'); // Ensure tool remains 'select' for editing.
            } else {
                // Single-click to select any token for path drawing
                lastClick.current = { time: now, tokenId: clickedToken.id };
                setSelectedItemIds([{ type: 'token', id: clickedToken.id }]);
                setEditingTokenId(null);
                setActiveTool('path');
            }
        } else {
            // Clicked on empty space
            setSelectedItemIds([]);
            setEditingTokenId(null);
        }
        return;
    }
    
    if (activeTool === 'arrow') {
        if (drawingState?.type === 'arrow' && drawingState.stage === 'defining-control') {
            saveStateToHistory();
            const { start, end, current } = drawingState;
            const newId = getNextId();
            const newArrow: ArrowAnnotation = { 
                id: newId, 
                type: AnnotationType.Arrow, 
                start, 
                end, 
                control: current, 
                color: arrowSettings.color, 
                isVisible: true,
                ...arrowSettings
            };
            setAnnotations(prev => [...prev, newArrow]);
            setLayers(prev => [{id: newId, type: 'annotation'}, ...prev]);
            setDrawingState(null);
        } else {
            setDrawingState({ type: 'arrow', stage: 'defining-end', start: point, current: point });
        }
    } else if (activeTool === 'path' && selectedItemIds.length === 1 && selectedItemIds[0].type === 'token') {
        saveStateToHistory();
        setDrawingState({ type: 'path', pathForTokenId: selectedItemIds[0].id, points: [point] });
    } else if (activeTool === 'circle') {
        setDrawingState({ type: 'circle', start: point, current: point });
    } else if (activeTool === 'brush') {
        setDrawingState({ type: 'brush', segments: [{ path: [point], settings: brushSettings }], lazyPoint: point });
    }
  };

  const handleMouseMove = (point: Point, screenPoint: Point) => {
    if (!drawingState) return;

    if (drawingState.type === 'zoom') {
        setDrawingState(prev => (prev?.type === 'zoom' ? { ...prev, current: screenPoint } : prev));
        return;
    }

    if (drawingState.type === 'path') {
        const lastPoint = drawingState.points[drawingState.points.length - 1];
        if (distSq(lastPoint, point) > 5 * 5) { // 5px distance threshold
            setDrawingState(prev => {
                if (prev?.type !== 'path') return prev;
                return { ...prev, points: [...prev.points, point] };
            });
        }
        return;
    }
    
    if (drawingState.type === 'brush') {
        const { lazyPoint: currentLazyPoint } = drawingState;
        const targetPoint = point;

        // amount = 1 means no smoothing. amount close to 0 means high smoothing.
        const smoothingAmount = brushSettings.smoothing > 0 ? 1 / (1 + brushSettings.smoothing * 0.5) : 1;

        const newLazyPoint = {
            x: currentLazyPoint.x + (targetPoint.x - currentLazyPoint.x) * smoothingAmount,
            y: currentLazyPoint.y + (targetPoint.y - currentLazyPoint.y) * smoothingAmount,
        };

        const lastSegment = drawingState.segments[drawingState.segments.length - 1];
        const lastActualPoint = lastSegment.path[lastSegment.path.length - 1];

        if (distSq(lastActualPoint, newLazyPoint) > 1) { // Add point if moved more than 1px
            setDrawingState(prev => {
                if (prev?.type !== 'brush' || prev.segments.length === 0) return prev;
                
                const newSegments = [...prev.segments];
                const latestSegment = { ...newSegments[newSegments.length - 1] };
                latestSegment.path = [...latestSegment.path, newLazyPoint];
                newSegments[newSegments.length - 1] = latestSegment;

                return { ...prev, segments: newSegments, lazyPoint: newLazyPoint };
            });
        } else {
             // Even if we don't add a point, update the lazy point state to keep it moving towards the cursor
             setDrawingState(prev => {
                 if(prev?.type !== 'brush') return prev;
                 return { ...prev, lazyPoint: newLazyPoint };
             });
        }
        return;
    }

    if (drawingState.type === 'enlarging') {
        const { tokenId, startY, startSize } = drawingState;
        const deltaY = point.y - startY;
        const SENSITIVITY = 0.01;
        const newSize = Math.max(0.2, Math.min(10.0, startSize - deltaY * SENSITIVITY));
        setTokens(currentTokens =>
            currentTokens.map(t => (t.id === tokenId ? { ...t, size: newSize } : t))
        );
    } else if (drawingState.type === 'moving') {
        const { itemType, itemId } = drawingState;
        if (itemType === 'token') {
             const { offset } = drawingState;
             const newPosition = { x: point.x - offset.x, y: point.y - offset.y };
             setTokens(currentTokens =>
                currentTokens.map(t => (t.id === itemId ? { ...t, position: newPosition } : t))
             );
        } else {
            const { current: prevPoint } = drawingState;
            const dx = point.x - prevPoint.x;
            const dy = point.y - prevPoint.y;
            
            setAnnotations(currentAnns => currentAnns.map(ann => {
                if (ann.id !== itemId) return ann;
                
                const movePoint = (p: Point) => ({ x: p.x + dx, y: p.y + dy });

                switch (ann.type) {
                    case AnnotationType.Arrow:
                        return { ...ann, start: movePoint(ann.start), end: movePoint(ann.end), control: movePoint(ann.control) };
                    case AnnotationType.Circle:
                        return { ...ann, center: movePoint(ann.center) };
                    case AnnotationType.BrushStroke:
                        return { 
                            ...ann, 
                            segments: ann.segments.map(segment => ({
                                ...segment,
                                path: segment.path.map(movePoint)
                            }))
                        };
                    default:
                        return ann;
                }
            }));
        }
    }
    
    setDrawingState(prev => (prev ? { ...prev, current: point } : null));
  };
  
  const handleMouseUp = (canvasRect: DOMRect | undefined) => {
    if (!drawingState) return;
    
    if (drawingState.type === 'path') {
        const { pathForTokenId, points } = drawingState;
        if (points.length > 1) { // Only add if the user actually drew something
            setTokens(prevTokens => prevTokens.map(token => {
                if (token.id === pathForTokenId) {
                    const newPath = token.path.length === 0 
                        ? [token.position, ...points] 
                        : [...token.path, ...points];
                    return { ...token, path: newPath };
                }
                return token;
            }));
        }
        setDrawingState(null);
        return;
    }

    if (drawingState.type === 'brush') {
        const { segments } = drawingState;
        // A real stroke was drawn if it has more than one point.
        if (segments.some(s => s.path.length > 1)) {
            const finishedSegment = segments[0]; // There's only one segment in drawingState for a single stroke
            setCurrentBrushAnnotation(prev => {
                if (!prev) {
                    // This is the first stroke of a new session.
                    return {
                        id: -1, // Temporary ID, will be replaced on commit
                        type: AnnotationType.BrushStroke,
                        segments: [finishedSegment],
                        isVisible: true,
                    };
                }
                // Add this stroke as a new segment to the current session's annotation.
                return { ...prev, segments: [...prev.segments, finishedSegment] };
            });
            setBrushRedoStack([]);
        }
        setDrawingState(null);
        return;
    }

    if (drawingState.type === 'zoom') {
        const { start, current } = drawingState; // These are screen points
        const dx = Math.abs(start.x - current.x);
        const dy = Math.abs(start.y - current.y);
        // Ensure zoom area is not too small (in screen pixels)
        if (dx > 10 && dy > 10) {
            handleZoom(start, current, canvasRect);
        }
        setDrawingState(null);
        return;
    }
    
    if (drawingState.type === 'enlarging') {
        setDrawingState(null);
        return;
    }

    if (drawingState.type === 'moving') {
        setDrawingState(null);
        return;
    }

    if (drawingState.type === 'cloning') {
        saveStateToHistory();
        const { tokenToClone, current } = drawingState;
        const { id, path, animationProgress, patrolForward, ...options } = tokenToClone;
        addToken(options.type, current, options);
        setDrawingState(null);
        return;
    }
    
    if (drawingState.type === 'arrow' && drawingState.stage === 'defining-end') {
        if (distSq(drawingState.start, drawingState.current) < 5 * 5) {
            setDrawingState(null);
            return;
        }
        const { start, current } = drawingState;
        const control = { x: (start.x + current.x) / 2, y: (start.y + current.y) / 2 };
        setDrawingState({ type: 'arrow', stage: 'defining-control', start, end: current, current: control });
        return;
    }

    const { type, start, current } = drawingState;
     if (distSq(start, current) < 5 * 5) {
        setDrawingState(null);
        return;
    }

    if (type === 'circle') {
        saveStateToHistory();
        const radius = Math.hypot(current.x - start.x, current.y - start.y);
        const newId = getNextId();
        setAnnotations(prev => [...prev, { id: newId, type: AnnotationType.Circle, center: start, radius, color: selectedColor1, isVisible: true }]);
        setLayers(prev => [{id: newId, type: 'annotation'}, ...prev]);
    }
    setDrawingState(null);
  };
  
  const handleTokenUpdate = (tokenId: number, updates: Partial<TokenState>) => {
    if (updates.outlineWidth !== undefined) {
        setLastOutlineWidth(updates.outlineWidth);
    }
    setTokens(prev => {
        const needsHistory = !Object.keys(updates).every(k => ['animationProgress', 'position', 'patrolForward'].includes(k));
        if(needsHistory) saveStateToHistory();
        return prev.map(t => {
            if (t.id !== tokenId) return t;

            const newT = { ...t, ...updates };
            if (updates.outlineColor2 === undefined && 'outlineColor2' in updates) {
                delete newT.outlineColor2;
            }
            return newT;
        });
    });
  }
  
  const handleWheel = (e: React.WheelEvent, point: Point) => {
    if (!mapDimensions) return;
    const TOKEN_HIT_RADIUS = 20;
    const tokenToUpdate = tokens.find(t => t.isVisible !== false && distSq(t.position, point) < (TOKEN_HIT_RADIUS * (t.size ?? 1)) ** 2);
    if (!tokenToUpdate) return;
    
    e.preventDefault();
    
    if (e.ctrlKey) {
       handleTokenUpdate(tokenToUpdate.id, { size: Math.max(0.2, Math.min(10.0, (tokenToUpdate.size ?? 1) - Math.sign(e.deltaY) * 0.1)) });
    } else if(tokenToUpdate.type !== UnitType.Text && tokenToUpdate.type !== UnitType.Other) {
      let currentNum = tokenToUpdate.number ?? (e.deltaY < 0 ? 0 : 2);
      if (e.deltaY < 0) {
        currentNum = currentNum >= 15 ? 1 : currentNum + 1;
      } else {
        currentNum = currentNum <= 1 ? 15 : currentNum - 1;
      }
      handleTokenUpdate(tokenToUpdate.id, { number: currentNum });
    }
  };

  const animationLoop = useCallback(() => {
    setTokens(currentTokens =>
      currentTokens.map(token => {
        if (token.path.length < 2 || token.isVisible === false || token.isAnimating === false) return token;

        const isPatrol = token.isPatrol ?? false;
        const animationSpeed = token.animationSpeed ?? 1.0;

        const pathLength = getPathLength(token.path);
        if (pathLength === 0) return token;
        
        const baseSpeed = 2; // map units per frame at 1x speed
        const progressIncrement = (baseSpeed * Math.abs(animationSpeed)) / pathLength;

        let newProgress = token.animationProgress;
        let isForward = token.patrolForward ?? true;

        if (isPatrol) {
          if (isForward) {
            newProgress += progressIncrement;
            if (newProgress >= 1.0) {
              newProgress = 1.0;
              isForward = false;
            }
          } else {
            newProgress -= progressIncrement;
            if (newProgress <= 0.0) {
              newProgress = 0.0;
              isForward = true;
            }
          }
        } else {
          // Non-patrol mode: infinite loop from start to end.
          newProgress += progressIncrement;
          if (newProgress >= 1.0) {
            newProgress %= 1.0; // Loop back to the beginning.
          }
        }

        const newPosition = getPointOnPath(token.path, newProgress);

        return { ...token, animationProgress: newProgress, position: newPosition, patrolForward: isForward };
      })
    );
    animationFrameId.current = requestAnimationFrame(animationLoop);
  }, []);

  useEffect(() => {
    animationFrameId.current = requestAnimationFrame(animationLoop);
    return () => {
        if(animationFrameId.current) cancelAnimationFrame(animationFrameId.current);
    };
  }, [animationLoop]);

  const clearPath = (tokenId: number) => {
    saveStateToHistory();
    setTokens(prev => prev.map(t => {
      if (t.id === tokenId) {
        const originalPosition = t.path.length > 0 ? t.path[0] : t.position;
        return { ...t, path: [], animationProgress: 0, position: originalPosition, patrolForward: true };
      }
      return t;
    }));
    setSelectedItemIds(prev => prev.filter(item => !(item.id === tokenId && item.type === 'token')));
    setActiveTool('select');
  };

  const clearAll = () => {
    saveStateToHistory();
    setTokens([]);
    setAnnotations([]);
    setLayers([]);
    setMapImage(null);
    setMapVideo(null);
    setMapDimensions(null);
    setSelectedItemIds([]);
    setActiveTool('select');
    setVideoCurrentTime(0);
    setVideoDuration(0);
  }

  const handleAddTextPreset = useCallback(() => {
    if (textPresets.some(p => p.content === newText.content && p.fontFamily === newText.fontFamily && p.size === newText.size)) {
        return;
    }
    const preset: TextPreset = { id: Date.now(), ...newText };
    setTextPresets(prev => [...prev, preset]);
  }, [newText, textPresets]);

  const handleDeleteTextPreset = useCallback((id: number) => {
    setTextPresets(prev => prev.filter(p => p.id !== id));
  }, []);

  const handleLoadTextPreset = useCallback((preset: TextPreset) => {
    setNewText({
        ...newText,
        content: preset.content,
        size: preset.size,
        fontFamily: preset.fontFamily,
    });
  }, [newText]);

  const handlePaletteColorChange = (index: number, newColor: string) => {
      saveStateToHistory();
      const oldColor = paletteColors[index];
      const newPalette = [...paletteColors];
      newPalette[index] = newColor;
      setPaletteColors(newPalette);

      if (selectedColor1 === oldColor) {
        setSelectedColor1(newColor);
      }
      if (selectedColor2 === oldColor) {
        setSelectedColor2(newColor);
      }
  };

  const handleNewTextChange = (state: NewTextState) => {
    if (state.outlineWidth !== newText.outlineWidth) {
        setLastOutlineWidth(state.outlineWidth);
    }
    setNewText(state);
  };
  
  const handleToggleVisibility = (id: number, type: 'token' | 'annotation') => {
      saveStateToHistory();
      if (type === 'token') {
          setTokens(prev => prev.map(t => t.id === id ? { ...t, isVisible: !(t.isVisible ?? true) } : t));
      } else {
          setAnnotations(prev => prev.map(a => a.id === id ? { ...a, isVisible: !(a.isVisible ?? true) } : a));
      }
  };

  const handleToggleGroupVisibility = useCallback((groupId: number) => {
      saveStateToHistory();
      const group = findGroupRec(layers, groupId);
      if (!group) return;
  
      const { tokens: tokenIds, annotations: annotationIds } = getAllItemIdsFromGroup(group);
  
      // Check if any item in the group is currently visible
      const isAnyVisible = 
          Array.from(tokenIds).some(id => tokens.find(t => t.id === id)?.isVisible ?? true) ||
          Array.from(annotationIds).some(id => annotations.find(a => a.id === id)?.isVisible ?? true);
          
      const newVisibility = !isAnyVisible;
  
      setTokens(prev => prev.map(t => tokenIds.has(t.id) ? { ...t, isVisible: newVisibility } : t));
      setAnnotations(prev => prev.map(a => annotationIds.has(a.id) ? { ...a, isVisible: newVisibility } : a));
  }, [layers, tokens, annotations, saveStateToHistory]);

  const handleLayersUpdate = (newLayers: LayerEntry[]) => {
      saveStateToHistory();
      setLayers(newLayers);
  };
  
  const handleCustomUnitUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;
    
    Array.from(files).forEach(file => {
        if (file.type === 'image/png' || file.type === 'image/gif') {
            const reader = new FileReader();
            const filename = file.name;
            const name = filename.substring(0, filename.lastIndexOf('.')) || filename;
            reader.onload = (e) => {
                const imageData = e.target?.result as string;
                if (imageData) {
                    setCustomUnits(prev => [...prev, { id: getNextId(), name, imageData }]);
                }
            };
            reader.readAsDataURL(file);
        }
    });

    if (event.target) {
        event.target.value = '';
    }
  };
  
  const handleCustomOtherUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;
    
    Array.from(files).forEach(file => {
        if (file.type === 'image/png' || file.type === 'image/gif') {
            const reader = new FileReader();
            const filename = file.name;
            const name = filename.substring(0, filename.lastIndexOf('.')) || filename;
            reader.onload = (e) => {
                const imageData = e.target?.result as string;
                if (imageData) {
                    setCustomOthers(prev => [...prev, { id: getNextId(), name, imageData }]);
                }
            };
            reader.readAsDataURL(file);
        }
    });

    if (event.target) {
        event.target.value = '';
    }
  };

  const handleDeleteCustomUnit = useCallback((id: number) => {
    saveStateToHistory();
    setCustomUnits(prev => prev.filter(unit => unit.id !== id));
  }, [saveStateToHistory]);

  const handleDeleteCustomOther = useCallback((id: number) => {
    saveStateToHistory();
    setCustomOthers(prev => prev.filter(unit => unit.id !== id));
  }, [saveStateToHistory]);

  const handleExportProject = useCallback(() => {
    const projectData: ProjectData = {
      mapImage,
      mapVideo,
      mapDimensions,
      tokens,
      annotations,
      paletteColors,
      nextId: nextId.current,
      customUnits,
      customOthers,
      layers,
    };

    const jsonString = JSON.stringify(projectData, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'battle-plan.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [mapImage, mapVideo, mapDimensions, tokens, annotations, paletteColors, customUnits, customOthers, layers]);

  const handleImportProject = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const text = e.target?.result;
        if (typeof text !== 'string') throw new Error('File is not a text file.');
        
        const data: Partial<ProjectData & { customArtillery?: CustomUnit[] }> = JSON.parse(text);

        if (!data || typeof data !== 'object') {
          throw new Error('Invalid project file format.');
        }
        
        const migratedTokens = (data.tokens || []).map(t => {
            const { auraSize, auraOpacity, auraColor2, artilleryType, ...rest } = t as any; // remove old props
            const token = {
              ...rest,
              isVisible: rest.isVisible ?? true,
              rotation: rest.rotation ?? 0,
              animationSpeed: rest.animationSpeed ?? 1.0,
              isPatrol: rest.isPatrol ?? false,
              isFlipped: rest.isFlipped ?? false,
              isAnimating: rest.isAnimating ?? true,
            };
            if (artilleryType) {
                (token as TokenState).otherType = artilleryType;
            }
            return token as TokenState;
        });
        
        const migratedAnnotations = (data.annotations || []).map(a => {
            if (a.type === AnnotationType.Arrow) {
                const oldArrow = a as any;
                const { strokeWidth, ...rest } = oldArrow;
                const strokeWidthStart = strokeWidth ?? oldArrow.strokeWidthStart ?? 5;
                const strokeWidthEnd = oldArrow.strokeWidthEnd ?? strokeWidthStart;
                
                return {
                    ...{ // Default values for properties that might be missing on old arrows
                        arrowheadLength: 15,
                        arrowheadWidth: 10,
                        isAnimated: true,
                        animatingCircleRadius: 4,
                        animatingCircleColor: a.color,
                        animationDuration: 2.5,
                    },
                    ...rest,
                    isVisible: a.isVisible ?? true,
                    strokeWidthStart,
                    strokeWidthEnd,
                };
            }
            if (a.type === AnnotationType.BrushStroke) {
                const oldStroke = a as any;

                if (oldStroke.segments) { // Already new format
                    return { ...a, isVisible: a.isVisible ?? true };
                }

                if (oldStroke.points && !oldStroke.paths) {
                    oldStroke.paths = [oldStroke.points];
                }

                if (oldStroke.paths) { // Old format with paths
                    const oldEffect = oldStroke.effect;
                    let newEffect: BrushSettings['effect'] = 'none';
                    if (oldEffect === 'flashing') newEffect = 'flashing';
                    if (oldEffect === 'breathing') newEffect = 'breathing';
                    if (oldEffect === 'rainbow-cycle') newEffect = 'rainbow-cycle';

                    const settings: BrushSettings = {
                        color: oldStroke.color,
                        size: oldStroke.size,
                        opacity: oldStroke.opacity ?? 1,
                        effect: newEffect,
                        glowColor: oldStroke.glowColor ?? '#FFFF00',
                        glowSize: oldStroke.glowSize ?? 0,
                        isNeon: oldStroke.isNeon ?? false,
                        smoothing: oldStroke.smoothing ?? 0,
                    };
                    const newSegments: BrushPathSegment[] = oldStroke.paths.map((path: Point[]) => ({
                        path,
                        settings,
                    }));

                    return {
                        id: oldStroke.id,
                        type: AnnotationType.BrushStroke,
                        segments: newSegments,
                        isVisible: oldStroke.isVisible ?? true,
                    };
                }
            }
            return { ...a, isVisible: a.isVisible ?? true };
        });

        setHistory([]);
        setRedoStack([]);
        setTokens(migratedTokens);
        setAnnotations(migratedAnnotations as Annotation[]);

        if (data.layers) {
            setLayers(data.layers);
        } else {
            const legacyLayers: LayerEntry[] = [
                ...migratedAnnotations.map(a => ({ id: a.id, type: 'annotation' as const })),
                ...migratedTokens.map(t => ({ id: t.id, type: 'token' as const })),
            ].reverse();
            setLayers(legacyLayers);
        }
        
        setSelectedItemIds([]);
        setDrawingState(null);
        setEditingTokenId(null);
        setActiveTool('select');
        setViewTransform(null);
        setCurrentBrushAnnotation(null);

        setMapImage(data.mapImage ?? null);
        setMapVideo(data.mapVideo ?? null);
        if (data.mapVideo) {
            setVideoCurrentTime(0);
            setVideoDuration(0); // This will be updated later by onLoadedMetadata
            setIsVideoPlaying(true);
        }

        setMapDimensions(data.mapDimensions ?? null);
        setPaletteColors(data.paletteColors ?? INITIAL_PALETTE_COLORS);
        setCustomUnits(data.customUnits ?? []);
        setCustomOthers(data.customOthers ?? data.customArtillery ?? []);
        nextId.current = data.nextId ?? Math.max(0, ...migratedTokens.map(t => t.id), ...migratedAnnotations.map(a => a.id)) + 1;

        if (event.target) {
            event.target.value = '';
        }
      } catch (error) {
        console.error('Failed to import project:', error);
        alert(t('project_import_error'));
        if (event.target) {
            event.target.value = '';
        }
      }
    };
    reader.readAsText(file);
  };
  
  const [canvasTokens, canvasAnnotations] = useMemo(() => {
        const tkns: TokenState[] = [];
        const anns: Annotation[] = [];
        for (const item of flattenedItemsForCanvas) {
            if (item.type === AnnotationType.Arrow || item.type === AnnotationType.Circle || item.type === AnnotationType.BrushStroke) {
                anns.push(item as Annotation);
            } else {
                tkns.push(item as TokenState);
            }
        }
        return [tkns, anns];
  }, [flattenedItemsForCanvas]);

    const handleResetTokenAnimation = useCallback((tokenId: number) => {
        saveStateToHistory();
        setTokens(prev => prev.map(t => {
            if (t.id === tokenId && t.path.length > 0) {
                return {
                    ...t,
                    position: t.path[0],
                    animationProgress: 0,
                    patrolForward: true,
                };
            }
            return t;
        }));
    }, [saveStateToHistory]);

    const handleToggleTokenAnimation = useCallback((tokenId: number) => {
        saveStateToHistory();
        setTokens(prev => prev.map(t => {
            if (t.id === tokenId) {
                return { ...t, isAnimating: !(t.isAnimating ?? true) };
            }
            return t;
        }));
    }, [saveStateToHistory]);

    const handleResetGroupAnimation = useCallback((groupId: number) => {
        saveStateToHistory();
        const group = findGroupRec(layers, groupId);
        if (!group) return;
    
        const { tokens: tokenIdsToReset } = getAllItemIdsFromGroup(group);
    
        setTokens(prev => prev.map(t => {
            if (tokenIdsToReset.has(t.id) && t.path.length > 0) {
                return {
                    ...t,
                    position: t.path[0],
                    animationProgress: 0,
                    patrolForward: true,
                };
            }
            return t;
        }));
    }, [layers, saveStateToHistory]);

    const handleToggleGroupAnimation = useCallback((groupId: number) => {
        saveStateToHistory();
        const group = findGroupRec(layers, groupId);
        if (!group) return;
    
        const { tokens: tokenIdsInGroup } = getAllItemIdsFromGroup(group);
        if (tokenIdsInGroup.size === 0) return;
    
        const animatableTokens = tokens.filter(t => tokenIdsInGroup.has(t.id) && t.path.length > 1);
        if (animatableTokens.length === 0) return;
    
        const isAnyAnimating = animatableTokens.some(t => t.isAnimating ?? true);
        const newAnimationState = !isAnyAnimating;
    
        setTokens(prev => prev.map(t => {
            if (tokenIdsInGroup.has(t.id)) {
                return { ...t, isAnimating: newAnimationState };
            }
            return t;
        }));
    }, [layers, tokens, saveStateToHistory]);

    const handleLanguageChange = useCallback(() => {
        setLanguage(lang => (lang === 'en' ? 'es' : 'en'));
    }, []);

    const handleSelectItems = useCallback((
        clickedItem: LayerItemIdentifier,
        isShiftClick: boolean,
        flatVisibleItems: LayerItemIdentifier[]
    ) => {
        let newSelection: LayerItemIdentifier[] = [];
        const isClickedItemSelected = selectedItemIds.some(
            item => item.id === clickedItem.id && item.type === clickedItem.type
        );

        if (isShiftClick && selectedItemIds.length > 0) {
            const lastSelectedItem = selectedItemIds[selectedItemIds.length - 1];
            
            const anchorIndex = flatVisibleItems.findIndex(
                item => item.id === lastSelectedItem.id && item.type === lastSelectedItem.type
            );
            const clickedIndex = flatVisibleItems.findIndex(
                item => item.id === clickedItem.id && item.type === clickedItem.type
            );

            if (anchorIndex !== -1 && clickedIndex !== -1) {
                const start = Math.min(anchorIndex, clickedIndex);
                const end = Math.max(anchorIndex, clickedIndex);
                newSelection = flatVisibleItems.slice(start, end + 1);
            } else {
                newSelection = [...selectedItemIds.filter(i => i.id !== clickedItem.id || i.type !== clickedItem.type), clickedItem];
            }
        } else {
            if (isClickedItemSelected && selectedItemIds.length === 1) {
                newSelection = [];
            } else {
                newSelection = [clickedItem];
            }
        }
        
        setSelectedItemIds(newSelection);

        if (newSelection.length === 1 && newSelection[0].type === 'token') {
            setActiveTool(t => (t === 'select' ? 'path' : t));
        } else {
            setActiveTool('select');
        }
    }, [selectedItemIds]);

    const handleAssignItemsToGroup = useCallback((targetGroupId: number | null) => {
        if (selectedItemIds.length === 0) return;
        saveStateToHistory();

        let newLayers = JSON.parse(JSON.stringify(layers));
        const itemsToMove = [...selectedItemIds];
        const itemKeysToMove = new Set(itemsToMove.map(i => `${i.type}-${i.id}`));

        const removeItemsRecursive = (entries: LayerEntry[]): LayerEntry[] => {
            return entries.filter(entry => {
                if (entry.type === 'group') {
                    entry.items = removeItemsRecursive(entry.items);
                    return true;
                }
                return !itemKeysToMove.has(`${entry.type}-${entry.id}`);
            });
        };
        newLayers = removeItemsRecursive(newLayers);

        if (targetGroupId === null) {
            newLayers.unshift(...itemsToMove);
        } else {
            let groupFound = false;
            const addItemsRecursive = (entries: LayerEntry[]) => {
                for (const entry of entries) {
                    if (entry.type === 'group' && entry.id === targetGroupId) {
                        entry.items.unshift(...itemsToMove);
                        groupFound = true;
                        return;
                    }
                    if (entry.type === 'group') {
                        addItemsRecursive(entry.items);
                        if(groupFound) return;
                    }
                }
            };
            addItemsRecursive(newLayers);
        }
        
        setLayers(newLayers);
    }, [layers, selectedItemIds, saveStateToHistory]);

    const handleBrushSettingsChange = (newSettings: BrushSettings) => {
      // When brush settings change mid-stroke, we need to create a new segment.
      setDrawingState(prev => {
          if (prev?.type !== 'brush') return prev;

          const lastSegment = prev.segments[prev.segments.length - 1];
          // If the current segment only has one point (the start point), just update its settings.
          // This allows changing settings before you start drawing the line.
          if (lastSegment && lastSegment.path.length <= 1) {
              const newSegments = [...prev.segments];
              newSegments[newSegments.length - 1] = { ...lastSegment, settings: newSettings };
              return { ...prev, segments: newSegments };
          }
          
          // If user has already drawn part of a line, commit that and start a new segment.
          if (lastSegment) {
              const lastPoint = lastSegment.path[lastSegment.path.length - 1];
              
              // We need to update the `currentBrushAnnotation` with the segment we just finished
              const finishedSegment = prev.segments[0];
              setCurrentBrushAnnotation(current => {
                 if (!current) {
                    return {
                        id: -1, type: AnnotationType.BrushStroke, segments: [finishedSegment], isVisible: true
                    }
                 }
                 return { ...current, segments: [...current.segments, finishedSegment]};
              });

              // And then start a new drawing state with the new settings
              return {
                  type: 'brush',
                  segments: [{ path: [lastPoint], settings: newSettings }],
                  lazyPoint: lastPoint
              };
          }
          return prev;
      });
      setBrushSettings(newSettings);
    };

    const isZooming = drawingState?.type === 'zoom';
    
    const handleThemeSettingsReset = useCallback(() => {
        localStorage.removeItem('themeSettings');
        setThemeSettings(INITIAL_THEME_SETTINGS);
    }, []);

  return (
    <div className="flex h-screen w-screen bg-gray-900 font-sans">
      <Toolbar 
        activeTool={activeTool}
        onToolSelect={setActiveTool}
        onMapUpload={handleMapUpload}
        onClearAll={clearAll}
        selectedColor1={selectedColor1}
        onColor1Change={setSelectedColor1}
        selectedColor2={selectedColor2}
        onColor2Change={setSelectedColor2}
        paletteColors={paletteColors}
        onPaletteColorChange={handlePaletteColorChange}
        onUndo={handleUndo}
        onRedo={handleRedo}
        newText={newText}
        onNewTextChange={handleNewTextChange}
        textPresets={textPresets}
        onAddTextPreset={handleAddTextPreset}
        onDeleteTextPreset={handleDeleteTextPreset}
        onLoadTextPreset={handleLoadTextPreset}
        arrowSettings={arrowSettings}
        onArrowSettingsChange={setArrowSettings}
        brushSettings={brushSettings}
        onBrushSettingsChange={handleBrushSettingsChange}
        selectedToken={selectedToken}
        onTokenUpdate={handleTokenUpdate}
        onClearPath={clearPath}
        onExportProject={handleExportProject}
        onImportProject={handleImportProject}
        customUnits={customUnits}
        onCustomUnitUpload={handleCustomUnitUpload}
        onDeleteCustomUnit={handleDeleteCustomUnit}
        customOthers={customOthers}
        onCustomOtherUpload={handleCustomOtherUpload}
        onDeleteCustomOther={handleDeleteCustomOther}
        globalTokenSize={globalTokenSize}
        onGlobalTokenSizeChange={setGlobalTokenSize}
        language={language}
        onLanguageChange={handleLanguageChange}
        arePathsVisible={arePathsVisible}
        onArePathsVisibleChange={setArePathsVisible}
        themeSettings={themeSettings}
        onThemeSettingsChange={setThemeSettings}
        onThemeSettingsReset={handleThemeSettingsReset}
        t={t}
      />
      <main className="flex-1 h-full relative" ref={canvasRef}>
        <Canvas
          mapImage={mapImage}
          mapVideo={mapVideo}
          mapDimensions={mapDimensions}
          tokens={canvasTokens}
          annotations={canvasAnnotations}
          currentBrushAnnotation={currentBrushAnnotation}
          onDrop={handleTokenDrop}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onWheel={handleWheel}
          drawingState={drawingState}
          activeTool={activeTool}
          selectedColor={selectedColor1}
          arrowSettings={arrowSettings}
          brushSettings={brushSettings}
          editingTokenId={editingTokenId}
          onTokenUpdate={handleTokenUpdate}
          onFinishEditing={() => setEditingTokenId(null)}
          viewTransform={viewTransform}
          onZoomReset={handleZoomReset}
          globalTokenSize={globalTokenSize}
          arePathsVisible={arePathsVisible}
          mapRotation={mapRotation}
          isMapRotating={isMapRotating}
          videoRef={videoRef}
          isVideoPlaying={isVideoPlaying}
          onVideoPlayPause={setIsVideoPlaying}
          videoCurrentTime={videoCurrentTime}
          onVideoTimeUpdate={setVideoCurrentTime}
          videoDuration={videoDuration}
          onVideoDurationChange={setVideoDuration}
          t={t}
        />
        {mapDimensions && (
            <RotationControl 
                rotation={mapRotation}
                onRotationChange={setMapRotation}
                onRotationStart={() => setIsMapRotating(true)}
                onRotationEnd={() => setIsMapRotating(false)}
                isZooming={isZooming}
                themeSettings={themeSettings}
                t={t}
            />
        )}
        <LayersPanel
          layers={layers}
          tokens={tokens}
          annotations={annotations}
          selectedItemIds={selectedItemIds}
          onSelectItems={handleSelectItems}
          onToggleVisibility={handleToggleVisibility}
          onToggleGroupVisibility={handleToggleGroupVisibility}
          onDeleteItem={deleteItem}
          onLayersUpdate={handleLayersUpdate}
          getNextId={getNextId}
          onResetTokenAnimation={handleResetTokenAnimation}
          onToggleTokenAnimation={handleToggleTokenAnimation}
          onResetGroupAnimation={handleResetGroupAnimation}
          onToggleGroupAnimation={handleToggleGroupAnimation}
          onAssignItemsToGroup={handleAssignItemsToGroup}
          themeSettings={themeSettings}
          t={t}
        />
      </main>
    </div>
  );
};

export default App;