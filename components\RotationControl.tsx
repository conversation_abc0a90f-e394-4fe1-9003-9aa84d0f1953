
import React, { useRef, useEffect } from 'react';
import { type ThemeSettings } from '../types';

interface RotationControlProps {
    rotation: number;
    onRotationChange: React.Dispatch<React.SetStateAction<number>>;
    onRotationStart: () => void;
    onRotationEnd: () => void;
    isZooming: boolean;
    themeSettings: ThemeSettings;
    t: (key: string) => string;
}

const normalizeAngle = (angle: number): number => {
    return (angle % 360 + 360) % 360;
};

const hexToRgba = (hex: string, alpha: number): string => {
    if (!hex || hex.length < 4) return `rgba(37, 99, 235, ${alpha})`; // Default blue
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};


export const RotationControl: React.FC<RotationControlProps> = ({ rotation, onRotationChange, onRotationStart, onRotationEnd, isZooming, themeSettings, t }) => {
    const controlRef = useRef<HTMLDivElement>(null);
    const isDraggingRef = useRef(false);
    const lastAngleRef = useRef(0);
    const resetTimeoutRef = useRef<number | null>(null);

    const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
        if (!controlRef.current) return;
        
        isDraggingRef.current = true;
        onRotationStart();
        
        const rect = controlRef.current.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        const startAngle = Math.atan2(e.clientY - centerY, e.clientX - centerX) * (180 / Math.PI);
        lastAngleRef.current = startAngle;
        
        window.addEventListener('mousemove', handleMouseMove);
        window.addEventListener('mouseup', handleMouseUp);
    };

    const handleMouseMove = (e: MouseEvent) => {
        if (!isDraggingRef.current || !controlRef.current) return;
        e.preventDefault();
        
        const rect = controlRef.current.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        const currentAngle = Math.atan2(e.clientY - centerY, e.clientX - centerX) * (180 / Math.PI);
        
        let deltaAngle = currentAngle - lastAngleRef.current;

        // Normalize the delta angle to avoid jumps when crossing -180/180 degrees
        if (deltaAngle > 180) {
            deltaAngle -= 360;
        } else if (deltaAngle < -180) {
            deltaAngle += 360;
        }

        lastAngleRef.current = currentAngle;
        
        onRotationChange(prevRotation => normalizeAngle(prevRotation + deltaAngle));
    };

    const handleMouseUp = () => {
        if (!isDraggingRef.current) return;
        isDraggingRef.current = false;
        onRotationEnd();
        window.removeEventListener('mousemove', handleMouseMove);
        window.removeEventListener('mouseup', handleMouseUp);
    };
    
    useEffect(() => {
        return () => { // Cleanup listeners and timeouts on unmount
            if (resetTimeoutRef.current) {
                clearTimeout(resetTimeoutRef.current);
            }
            window.removeEventListener('mousemove', handleMouseMove);
            window.removeEventListener('mouseup', handleMouseUp);
        }
    }, []);
    
    const handleResetClick = (e: React.MouseEvent) => {
        e.stopPropagation();
        if (resetTimeoutRef.current) return;

        if (rotation > 180 && rotation < 360) {
            // Animate to 360, which is visually the same as 0, but forces a clockwise (shortest path) animation from angles > 180.
            onRotationChange(360);
            
            // After the animation (300ms), silently snap the state from 360 to 0 without animation.
            resetTimeoutRef.current = window.setTimeout(() => {
                onRotationStart(); // Disables CSS transition
                onRotationChange(0); // Snap state to 0
                requestAnimationFrame(() => {
                    onRotationEnd(); // Re-enables CSS transition after the snap has been painted
                    resetTimeoutRef.current = null;
                });
            }, 300); // This must match the transition duration in Canvas.tsx
        } else {
            // For angles 0-180, a direct animation to 0 is the shortest path.
            onRotationChange(0);
        }
    }

    return (
        <div 
            ref={controlRef}
            className={`fixed bottom-8 right-8 z-20 w-32 h-32 rounded-full bg-gray-800/80 backdrop-blur-md shadow-2xl flex items-center justify-center select-none touch-none transition-all duration-300 ease-in-out ${
                isZooming
                ? 'scale-50 opacity-20 pointer-events-none'
                : 'cursor-grab active:cursor-grabbing'
            }`}
            onMouseDown={handleMouseDown}
            title={t('rotation_control_title')}
        >
            {/* Inner "Auto" reset button */}
            <button 
                onClick={handleResetClick}
                onMouseDown={(e) => e.stopPropagation()}
                style={{ backgroundColor: hexToRgba(themeSettings.buttonColor, 0.9) }}
                className="w-16 h-16 rounded-full hover:brightness-90 transition-all shadow-lg flex flex-col items-center justify-center text-white cursor-pointer relative overflow-hidden"
                title={t('rotation_control_reset')}
            >
                {/* Rotating indicator triangle */}
                <div
                    className="absolute inset-0 pointer-events-none"
                    style={{
                        transform: `rotate(${rotation}deg)`,
                        transition: 'transform 0.1s linear',
                    }}
                >
                    <svg viewBox="0 0 100 100" className="w-full h-full overflow-visible">
                        <polygon points="50,20 58,35 42,35" fill="rgba(34, 211, 238, 0.7)" />
                    </svg>
                </div>
                
                {/* Text content */}
                <div className="relative z-10 flex flex-col items-center justify-center">
                    <span className="text-lg font-bold">{Math.round(rotation % 360)}°</span>
                    <span className="text-xs font-semibold uppercase">{t('rotation_control_auto')}</span>
                </div>
            </button>
        </div>
    );
};
