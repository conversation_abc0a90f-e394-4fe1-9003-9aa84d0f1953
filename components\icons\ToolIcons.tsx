
import React from 'react';

const IconWrapper: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className = "w-full h-full" }) => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className={className}>
        {children}
    </svg>
);

export const SelectIcon: React.FC = () => (
    <IconWrapper>
        <path d="M15.494 13.04l-2.617 2.617a.75.75 0 0 1-1.06 0l-2.617-2.617L4.35 17.882a.75.75 0 1 1-1.06-1.061l4.853-4.852a.75.75 0 0 1 1.06 0l2.617 2.617 2.617-2.617a.75.75 0 0 1 1.06 0l4.853 4.852a.75.75 0 1 1-1.061 1.06l-4.852-4.852Z" />
        <path d="M15.494 3.28l-2.617 2.617a.75.75 0 0 1-1.06 0L9.2 3.28.752 11.728a.75.75 0 0 1-1.06-1.06L8.14.22a.75.75 0 0 1 1.06 0l2.617 2.617L14.435.22a.75.75 0 0 1 1.06 0l8.448 8.448a.75.75 0 0 1-1.06 1.06L15.494 3.28Z" />
    </IconWrapper>
);

export const MoveIcon: React.FC = () => (
    <IconWrapper>
      <path d="M10 21v-4H6l6-6-6-6h4V1h4v4h4l-6 6 6 6h-4v4h-4z" />
    </IconWrapper>
);

export const BrushIcon: React.FC = () => (
    <IconWrapper>
        <path d="M3.5 16.5a.5.5 0 0 1 .5-.5h2a.5.5 0 0 1 .5.5v2.25a.25.25 0 0 1-.25.25h-2.5a.25.25 0 0 1-.25-.25V16.5Z" />
        <path d="M19.344 3.844a1.75 1.75 0 0 0-2.475 0L7.156 13.557a.25.25 0 0 0 .177.424h5.417a.25.25 0 0 0 .22-.152L19.344 6.319a1.75 1.75 0 0 0 0-2.475Z" />
    </IconWrapper>
);

export const CloneIcon: React.FC = () => (
    <IconWrapper>
        <path fillRule="evenodd" d="M4.5 3A1.5 1.5 0 0 0 3 4.5v10.5A1.5 1.5 0 0 0 4.5 16.5h3.75a.75.75 0 0 1 0 1.5H4.5A3 3 0 0 1 1.5 15V4.5A3 3 0 0 1 4.5 1.5h10.5A3 3 0 0 1 18 4.5v3.75a.75.75 0 0 1-1.5 0V4.5A1.5 1.5 0 0 0 15 3H4.5Z" clipRule="evenodd" />
        <path d="M8.25 7.5a.75.75 0 0 0-.75.75v10.5a.75.75 0 0 0 .75.75h10.5a.75.75 0 0 0 .75-.75V8.25a.75.75 0 0 0-.75-.75H8.25ZM9 9h9v9H9V9Z" />
    </IconWrapper>
);

export const ZoomIcon: React.FC = () => (
    <IconWrapper>
        <path fillRule="evenodd" d="M10.5 3.75a6.75 6.75 0 1 0 0 13.5 6.75 6.75 0 0 0 0-13.5ZM2.25 10.5a8.25 8.25 0 1 1 14.59 5.28l4.22 4.22a.75.75 0 1 1-1.06 1.06l-4.22-4.22A8.25 8.25 0 0 1 2.25 10.5Z" clipRule="evenodd" />
    </IconWrapper>
);

export const EraserIcon: React.FC = () => (
    <IconWrapper>
        <path fillRule="evenodd" d="M11.325 3.013c.473-.423 1.218-.396 1.652.07l7.5 8.25c.434.466.406 1.21-.068 1.652l-8.25 7.5c-.466.434-1.21.406-1.652-.068l-7.5-8.25a1.25 1.25 0 0 1 .068-1.652l8.25-7.5Zm.514 1.523L4.25 12l7.589 6.96 6.96-7.589-7.5-8.25Z" clipRule="evenodd" />
    </IconWrapper>
);

export const PathIcon: React.FC = () => (
    <IconWrapper>
        <path d="M3.375 4.5a.75.75 0 0 1 .75-.75h15a.75.75 0 0 1 0 1.5h-15a.75.75 0 0 1-.75-.75ZM3.375 7.5a.75.75 0 0 1 .75-.75h15a.75.75 0 0 1 0 1.5h-15a.75.75 0 0 1-.75-.75ZM3.375 10.5a.75.75 0 0 1 .75-.75h15a.75.75 0 0 1 0 1.5h-15a.75.75 0 0 1-.75-.75ZM3.375 13.5a.75.75 0 0 1 .75-.75h15a.75.75 0 0 1 0 1.5h-15a.75.75 0 0 1-.75-.75ZM3.375 16.5a.75.75 0 0 1 .75-.75h15a.75.75 0 0 1 0 1.5h-15a.75.75 0 0 1-.75-.75ZM3.375 19.5a.75.75 0 0 1 .75-.75h15a.75.75 0 0 1 0 1.5h-15a.75.75 0 0 1-.75-.75Z" />
    </IconWrapper>
);

export const ArrowIcon: React.FC = () => (
    <IconWrapper>
        <path fillRule="evenodd" d="M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z" clipRule="evenodd" />
    </IconWrapper>
);

export const CircleIcon: React.FC = () => (
    <IconWrapper>
        <path fillRule="evenodd" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM1.5 12a10.5 10.5 0 1 1 21 0 10.5 10.5 0 0 1-21 0Z" clipRule="evenodd" />
    </IconWrapper>
);

export const UploadIcon: React.FC = () => (
    <IconWrapper>
        <path fillRule="evenodd" d="M11.25 2.25c.414 0 .75.336.75.75v11.59l2.22-2.22a.75.75 0 1 1 1.06 1.06l-3.75 3.75a.75.75 0 0 1-1.06 0L8.22 13.38a.75.75 0 1 1 1.06-1.06l2.22 2.22V3a.75.75 0 0 1 .75-.75Z" clipRule="evenodd" />
        <path d="M3.75 16.5a.75.75 0 0 0 0 1.5h16.5a.75.75 0 0 0 0-1.5H3.75Z" />
    </IconWrapper>
);

export const TrashIcon: React.FC = () => (
    <IconWrapper>
        <path fillRule="evenodd" d="M16.5 4.478v.227a48.816 48.816 0 0 1-3.878.512.75.75 0 1 1-.256-1.478A48.583 48.583 0 0 0 16.5 3.542Z" clipRule="evenodd" />
        <path fillRule="evenodd" d="M5.25 5.5A2.25 2.25 0 0 1 7.5 3.25h9A2.25 2.25 0 0 1 18.75 5.5v13.5A2.25 2.25 0 0 1 16.5 21.25h-9A2.25 2.25 0 0 1 5.25 19v-13.5ZM7.5 4.75a.75.75 0 0 0-.75.75v13.5a.75.75 0 0 0 .75.75h9a.75.75 0 0 0 .75-.75V5.5a.75.75 0 0 0-.75-.75h-9Z" clipRule="evenodd" />
    </IconWrapper>
);

export const UndoIcon: React.FC = () => (
    <IconWrapper>
        <path fillRule="evenodd" d="M12.5 8c-2.65 0-5.05.99-6.9 2.6L2 7v9h9l-3.62-3.62c1.39-1.16 3.16-1.88 5.12-1.88 3.54 0 6.55 2.31 7.6 5.5l2.37-.78C21.08 11.03 17.15 8 12.5 8z" clipRule="evenodd" />
    </IconWrapper>
);

export const RedoIcon: React.FC = () => (
    <IconWrapper>
        <path fillRule="evenodd" d="M18.4 10.6c-1.85-1.61-4.25-2.6-6.9-2.6-3.54 0-6.55 2.31-7.6 5.5l-2.37-.78C3.42 8.03 7.35 5 12 5c2.96 0 5.74 1.25 7.65 3.25L23 5v9h-9l3.4-3.4z" clipRule="evenodd" />
    </IconWrapper>
);

export const ExportIcon: React.FC = () => (
    <IconWrapper>
        <path fillRule="evenodd" d="M12 2.25a.75.75 0 0 1 .75.75v11.59l2.22-2.22a.75.75 0 1 1 1.06 1.06l-3.75 3.75a.75.75 0 0 1-1.06 0L8.22 13.38a.75.75 0 1 1 1.06-1.06l2.22 2.22V3a.75.75 0 0 1 .75-.75Z" clipRule="evenodd" transform="translate(0, 24) scale(1, -1) translate(0, -24)" />
        <path d="M3.75 16.5a.75.75 0 0 0 0 1.5h16.5a.75.75 0 0 0 0-1.5H3.75Z" />
    </IconWrapper>
);

export const ImportIcon: React.FC = () => (
    <IconWrapper>
        <path fillRule="evenodd" d="M11.25 2.25c.414 0 .75.336.75.75v11.59l2.22-2.22a.75.75 0 1 1 1.06 1.06l-3.75 3.75a.75.75 0 0 1-1.06 0L8.22 13.38a.75.75 0 1 1 1.06-1.06l2.22 2.22V3a.75.75 0 0 1 .75-.75Z" clipRule="evenodd" />
        <path d="M3.75 16.5a.75.75 0 0 0 0 1.5h16.5a.75.75 0 0 0 0-1.5H3.75Z" />
    </IconWrapper>
);

export const BookmarkSquareIcon: React.FC = () => (
    <IconWrapper className="w-5 h-5">
        <path fillRule="evenodd" d="M5.25 3A2.25 2.25 0 0 0 3 5.25v13.5A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V5.25A2.25 2.25 0 0 0 18.75 3H5.25ZM6 6.75A.75.75 0 0 1 6.75 6h10.5a.75.75 0 0 1 .75.75v9.345a.75.75 0 0 1-1.28.53l-4.22-4.22a.75.75 0 0 0-1.06 0l-4.22 4.22A.75.75 0 0 1 6 16.095V6.75Z" clipRule="evenodd" />
    </IconWrapper>
);

export const ChevronDownIcon: React.FC = () => (
    <IconWrapper className="w-5 h-5">
        <path fillRule="evenodd" d="M12.53 16.03a.75.75 0 0 1-1.06 0l-7.5-7.5a.75.75 0 1 1 1.06-1.06L12 14.44l6.97-6.97a.75.75 0 1 1 1.06 1.06l-7.5 7.5Z" clipRule="evenodd" />
    </IconWrapper>
);

export const ChevronUpIcon: React.FC = () => (
    <IconWrapper className="w-5 h-5">
        <path fillRule="evenodd" d="M11.47 7.97a.75.75 0 0 1 1.06 0l7.5 7.5a.75.75 0 1 1-1.06 1.06L12 9.56l-6.97 6.97a.75.75 0 0 1-1.06-1.06l7.5-7.5Z" clipRule="evenodd" />
    </IconWrapper>
);

export const PlusIcon: React.FC = () => (
    <IconWrapper className="w-3.5 h-3.5">
        <path fillRule="evenodd" d="M12 3.75a.75.75 0 0 1 .75.75v6.75h6.75a.75.75 0 0 1 0 1.5h-6.75v6.75a.75.75 0 0 1-1.5 0v-6.75H4.5a.75.75 0 0 1 0-1.5h6.75V4.5a.75.75 0 0 1 .75-.75Z" clipRule="evenodd" />
    </IconWrapper>
);

export const MinusIcon: React.FC = () => (
    <IconWrapper className="w-3.5 h-3.5">
        <path fillRule="evenodd" d="M3.75 12a.75.75 0 0 1 .75-.75h15a.75.75 0 0 1 0 1.5h-15a.75.75 0 0 1-.75-.75Z" clipRule="evenodd" />
    </IconWrapper>
);

export const EyeIcon: React.FC = () => (
    <IconWrapper>
        <path d="M2.036 12.322a1.012 1.012 0 0 1 0-.639l4.25-7.5a1.012 1.012 0 0 1 1.732 0l4.25 7.5a1.012 1.012 0 0 1 0 .639l-4.25 7.5a1.012 1.012 0 0 1-1.732 0l-4.25-7.5Z" />
        <path d="M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z" />
    </IconWrapper>
);

export const EyeSlashIcon: React.FC = () => (
    <IconWrapper>
        <path d="M3.53 2.47a.75.75 0 0 0-1.06 1.06l18 18a.75.75 0 1 0 1.06-1.06l-18-18ZM22.678 11.678a.75.75 0 1 0-1.06-1.06L19.5 12.73V12a10.5 10.5 0 0 0-19.143-3.093l-1.365-1.365A.75.75 0 0 0 1.06 8.59l1.572 1.572C4.12 11.39 5.86 12 7.5 12h.127l2.115 2.115c-1.33.48-2.43.99-3.32 1.488a.75.75 0 0 0-.28 1.047l.828.828a.75.75 0 0 0 1.047-.28c.905-.51 2.06-1.04 3.44-1.557l1.328 1.328c-.09.02-.18.04-.27.06a.75.75 0 0 0-.53.923l.428 1.07a.75.75 0 0 0 .923.53l.27-.06c.6-.13 1.18-.28 1.73-.44l2.128 2.128a.75.75 0 1 0 1.06-1.06l-2.122-2.122Z" />
        <path d="M15.75 12c0 .814-.146 1.596-.412 2.322l-1.36-1.36a2.25 2.25 0 0 0-3.182-3.182l-1.36-1.36C10.154 8.146 10.936 8 11.75 8c2.071 0 3.75 1.679 3.75 3.75Z" />
    </IconWrapper>
);

export const XIcon: React.FC = () => (
    <IconWrapper>
        <path fillRule="evenodd" d="M5.47 5.47a.75.75 0 0 1 1.06 0L12 10.94l5.47-5.47a.75.75 0 1 1 1.06 1.06L13.06 12l5.47 5.47a.75.75 0 1 1-1.06 1.06L12 13.06l-5.47 5.47a.75.75 0 0 1-1.06-1.06L10.94 12 5.47 6.53a.75.75 0 0 1 0-1.06Z" clipRule="evenodd" />
    </IconWrapper>
);

export const ChevronRightIcon: React.FC = () => (
    <IconWrapper className="w-5 h-5">
        <path fillRule="evenodd" d="M8.22 5.22a.75.75 0 0 1 1.06 0l6 6a.75.75 0 0 1 0 1.06l-6 6a.75.75 0 0 1-1.06-1.06L13.69 12 8.22 6.28a.75.75 0 0 1 0-1.06Z" clipRule="evenodd" />
    </IconWrapper>
);

export const ChevronLeftIcon: React.FC = () => (
    <IconWrapper className="w-5 h-5">
        <path fillRule="evenodd" d="M15.78 18.78a.75.75 0 0 1-1.06 0l-6-6a.75.75 0 0 1 0-1.06l6-6a.75.75 0 1 1 1.06 1.06L10.31 12l5.47 5.47a.75.75 0 0 1 0 1.06Z" clipRule="evenodd" />
    </IconWrapper>
);

export const LayersIcon: React.FC = () => (
    <IconWrapper>
        <path d="M11.99 18.54l-7.37-5.73L3 14.07l9 7 9-7-1.63-1.27-7.38 5.74zM12 16l7.36-5.73L21 9l-9-7-9 7 1.63 1.27L12 16z" />
    </IconWrapper>
);

export const EnlargeIcon: React.FC = () => (
    <IconWrapper>
        <path fillRule="evenodd" d="M15 3.75a.75.75 0 0 1 .75.75v4.5a.75.75 0 0 1-1.5 0v-2.69l-4.72 4.72a.75.75 0 1 1-1.06-1.06l4.72-4.72h-2.69a.75.75 0 0 1 0-1.5h4.5ZM3.75 15a.75.75 0 0 1 .75.75v2.69l4.72-4.72a.75.75 0 1 1 1.06 1.06l-4.72 4.72h2.69a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1-.75-.75v-4.5a.75.75 0 0 1 .75-.75Z" clipRule="evenodd" />
    </IconWrapper>
);

export const SkipToStartIcon: React.FC = () => (
    <IconWrapper>
        <path fillRule="evenodd" d="M6 5.25a.75.75 0 0 1 .75-.75h1.5a.75.75 0 0 1 .75.75v13.5a.75.75 0 0 1-.75-.75h-1.5a.75.75 0 0 1-.75-.75V5.25Zm8.28 1.03a.75.75 0 0 1 .72 1.22l-5.25 4.5a.75.75 0 0 1 0 .998l5.25 4.5a.75.75 0 1 1-.96 1.158l-5.25-4.5a2.25 2.25 0 0 1 0-2.992l5.25-4.5a.75.75 0 0 1 .24-.164Z" clipRule="evenodd" />
    </IconWrapper>
);

export const PlayIcon: React.FC = () => (
    <IconWrapper>
        <path fillRule="evenodd" d="M4.5 5.653c0-1.426 1.529-2.33 2.779-1.643l11.54 6.647c1.295.748 1.295 2.538 0 3.286L7.279 20.99c-1.25.72-2.779-.217-2.779-1.643V5.653Z" clipRule="evenodd" />
    </IconWrapper>
);

export const PauseIcon: React.FC = () => (
    <IconWrapper>
        <path fillRule="evenodd" d="M6.75 5.25a.75.75 0 0 1 .75.75v12a.75.75 0 0 1-1.5 0v-12a.75.75 0 0 1 .75-.75Zm9 0a.75.75 0 0 1 .75.75v12a.75.75 0 0 1-1.5 0v-12a.75.75 0 0 1 .75-.75Z" clipRule="evenodd" />
    </IconWrapper>
);

export const SnapArrowIcon: React.FC = () => (
    <IconWrapper>
        <path d="M12 8l-6 6h12l-6-6z" />
    </IconWrapper>
);
